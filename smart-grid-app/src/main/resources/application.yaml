# 项目相关基础配置(统一配置）
ruoyi:
  # 开发环境服务器配置
  msg-bus:
    # 启用消息总线
    enable: true
    # 使用Redis Pub/Sub
    type: redis_channel
    # 单通道模式
    topic-handle-style: mpsc
    # 异步处理消息
    msg-handle-style: async

server:
  # 服务器的HTTP端口，默认为8080（同一个物理节点上部署端口不可以相同）
  port: 8080
  #  port: 4217
  servlet:
    # 应用的访问路径（不同应用路径不同）
    context-path: /smart-grid
# application.properties
jwt.expire-time:
  3000*20 # 1小时（毫秒）
# Spring配置(统一配置）
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages

  application:
    name: smart-grid
  profiles:
    default: dev

--- # SpringDoc 配置（应用模块可以省略这个注释）
springdoc:
  swagger-ui:
    # 指定 Swagger UI 前端访问路径
    path: /swagger-ui.html
    # 持久化数据是否需要认证
    persist-authorization: false
  group-configs:
    - group: 全部接口
      packages-to-scan: com.zhr
thread-pool:
  enabled: true

--- # magic-api 配置
magic-api:
  # 配置web开发页面，可空，为空时不开启
  # web: /magic/web
  resource:
    # type可以为file、database、redis
    type: database
    # 使用本地文件存储magic-api数据
    # location: ./magic-api-resource
    # 是否是只读模式，生产环境建议开启
    readonly: false
    # 指定数据源在magic代码中的key前缀
    prefix: default
    # 数据库中的表名称
    table-name: magic_api_file_v2
    # 指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    # datasource: magic
  push-path: /magic/_magic-api-sync #远程推送的路径，默认为/_magic-api-sync
  # 接口前缀
  prefix: /
  # 驼峰转换，默认保持原样
  sql-column-case: camel
  # 是否打印sql，默认开启
  show-sql: true
  # 是否持久化保存ResponseBody，接口结果较大时，不建议开启
  persistence-response-body: false
  # 是否允许覆盖应用接口，默认false
  allow-override: false
  backup: #备份相关配置
    enable: true #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    # datasource: magic  #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    table-name: magic_backup_record_v2 #使用数据库存储备份时的表名
smartgrid:
  # 默认的阈值，用在高压评估
  threshold: 30
  # 默认的查询时间跨度，用在高压评估
  day: 7

#驾车
#mapPath:
#  path1: https://map.sgcc.com.cn/rest/v1/direction/driving?output=JSON&extensions=all&strategy=0&ferry=0&origin=
#  path2: destination=

# 步行
mapPath:
  path1: https://map.sgcc.com.cn/rest/v1/direction/walking?origin=
  path2: destination=

# 开关处理标志：外网查询数据库、内网调用接口 true：内网 false：外网
kg-handler:
  mode: true
# 变电站处理标志 true：内网 false：外网
bdz-handler:
  mode: false

# 骑行
#mapPath:
#  path1: https://map.sgcc.com.cn/rest/v1/direction/bicycling?origin=
#  path2: destination=

# 查询设备坐标的网址
selectDeviceCoords:
  path: http://db.zhrj.com:8089/amap-gateway-service/amap-sdk-service/query/disSearch/queryDeviceById

# 查询设备曲线的网址
selectDeviceCurve:
  path: http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/amap-app-service/loadanalysis/loadrate/queryEquipPsrData

# 电网一张图地址
pmsUrl:
  baseUrl: http://pms.kjyzt.js.sgcc.com.cn:32080 # 基础URL
  amapAppServiceUrl: http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/amap-app-service # amap-app-service服务URL
  spaceSelectDevice: http://pms.kjyzt.js.sgcc.com.cn:32080/erccachesvr/geoCenter/spatialAnalysis/queryPSRByPolygon # 空间查询设备

#http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/pms-amap-components-service/new/devicecard/getBaseInfo线路信息
spring:
  datasource:
    druid:
      max-active: 60000
      max-wait: 1800000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
