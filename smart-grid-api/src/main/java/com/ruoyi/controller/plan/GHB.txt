curl --request POST \
  --url http://pms.kjyzt.js.sgcc.com.cn:32080/erccachesvr/geoCenter/spatialAnalysis/queryPSRByPolygon \
  --header 'Accept: application/json, text/plain, */*' \
  --header 'Accept-Encoding: deflate, gzip' \
  --header 'Accept-Language: zh-CN,zh;q=0.9' \
  --header 'Cache-Control: no-cache' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json;charset=UTF-8' \
  --header 'Cookie: auth_token=96dcf9b3d5a4098713055f0544e1998da06c33c5d5111b8689f5ff23a972121f23a983df7b8b4de80448f43f34895d2c51a05490d2b5bb1904e1249a0bb376aa567a97ed32c56dd7306dcd82ad5b485a3b668de655dc9972877527cbbb0416d17285eaff0b34151ae35c2a5adc4273f5; _at=eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMzk1MzIzODkiLCJpYXQiOjE3NTM1MTM0NDksInN1YiI6Inh1cmIiLCJleHAiOjE3NTM1OTk4NDl9.n4J-miMXCZ2hHOeKVxsZiid4d4qLxr-11YymIdvmKBk; SESSION=89a191d0-8774-4802-86ed-53e10eddefc4' \
  --header 'Origin: http://pms.kjyzt.js.sgcc.com.cn:32080' \
  --header 'Pragma: no-cache' \
  --header 'Referer: http://pms.kjyzt.js.sgcc.com.cn:32080/pms-amap-ui/?2D3C3641=x0u4r2b092551067a2b6619580341fb0&D5113FAF=103992503225308792b611b590041123&3BD058E2=273E7885323F4832C5E01F0074C2E00F&744B4F81=69D2A05BBBDD4286&jsIdsDomain=' \
  --header 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36' \
  --data '{
	"polygon": "",
	"srs": "EPSG:3857",
	"psrQueryInfo": {
		"psrQueryList": [
			{
				"psrType": "zf04",
				"whereClause": "1=1",
				"attrNameList": null,
				"distribution": 0
			},
			{
				"psrType": "zf07",
				"whereClause": "1=1",
				"attrNameList": null,
				"distribution": 0
			},
			{
				"psrType": "wlgt",
				"whereClause": "1=1",
				"attrNameList": null,
				"distribution": 0
			},
            {
				"psrType": "0103",
				"whereClause": "1=1",
				"attrNameList": null,
				"distribution": 0
			}
            
		],
		"attrNameList": [
			"classId",
			"psrId",
			"psrType",
			"psrTypeName",
			"psrName",
			"zoneId",
			"ZoneName",
			"innerTransId",
			"srcId",
			"srcName",
			"portList",
			"portNameList",
			"vlevelName",
			"vlevelCode",
			"VOLTAGE_LE",
			"coordinate",
			"feederId",
			"feederName",
			"useNature",
			"chargedState",
			"switchStatus",
			"distribution",
			"maintCrew",
			"maintCrewName",
			"maintOrg",
			"maintOrgName",
			"cityOrg",
			"cityOrgName",
			"provinceId",
			"crossFeederId",
			"crossFeederProvince",
			"isProvinceContainer",
			"direction",
			"dataSource",
			"plantType",
			"pubPrivFlag",
			"siteName",
			"zoneName"
		]
	}
}'