package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.QueryDevBo;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.plan.TestService;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.text.TrendTextCalcServiceImpl;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.entity.psm.HttpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 方案
 */
@RestController
@RequestMapping("/planTest")
@SaIgnore
public class PlanTestController {

    @Autowired
    IPlanTestService iPlanTestService;


    @Autowired
    TrendTextCalcServiceImpl textTrendCalcService;

    @Autowired
    TestService testService;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 用于测试线段
     */
    @GetMapping("/getSegList")
    public R<ArrayList<SegBetweenVo>> getSegList(@RequestParam String feederId, @RequestParam(required = false) String deviceId) {

        ArrayList<SegBetween> segBetweenList = iPlanTestService.getSegBetweenList(feederId, deviceId);
        return R.ok(NodeUtils.toSegBetweenVoList(segBetweenList));
    }

    /**
     * 用于测试线段
     */
    @PostMapping("/generatePlan")
    public R<List<Plan>> getSegList(@RequestBody GeneratePlanBo generatePlanBo) {

        List<Plan> plans = iPlanTestService.generateGridPlan(generatePlanBo);
        return R.ok(plans);
    }

    /**
     * 用于测试线段
     */
    @GetMapping("/getQueryDevs")
    public R<List<QueryDevBo>> getSegList() {
        return R.ok(testService.query());
    }

    /**
     * 获取联络开关
     */
    @GetMapping("/getContactKgs")
    public R<ArrayList<NodeVo>> getContactKgs(@RequestParam String feederId) {

        ArrayList<Node> result = iPlanTestService.getContactKgs(feederId);
        return R.ok(NodeUtils.toNodeVos(result));
    }

    /**
     * 获取联络开关对应各个主干路径都主干开关的各个分段
     */
    @GetMapping("/getAllContactKgSegList")
    public R<List<HashMap<String, Object>>> getAllContactKgSegList(@RequestParam String feederId) {
        return R.ok(iPlanTestService.getAllContactKgSegList(feederId));
    }

    /**
     * 获取主干路径
     */
    @GetMapping("/getMainPath")
    public R<List<NodeVo>> getMainPath(@RequestParam String feederId) {
        return R.ok(NodeUtils.toNodeVos(iPlanTestService.getMainPath(feederId)));
    }

    @Resource
    private FeederDeviceMapper feederDeviceMapper;
    /**
     * 测试方法
     * 获取指定的feederId的地理位置，然后将地理位置转换为魔卡托
     * 获取10DKX-53969
     * 10DKX-453297
     * 10DKX-368801
     * 10DKX-284193
     * 10DKX-300705
     * 10DKX-180321
     * 10DKX-463361
     * 10DKX-463361
     * 10DKX-570961
     * 10DKX-274225
     * 10DKX-340673
     * 10DKX-142769
     * 10DKX-172449
     * 这些指定路线
     */
    @GetMapping("/test")
    public void test() throws Exception {
        LambdaQueryWrapper<DeviceFeeder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceFeeder::getPsrId, "10DKX-53969","10DKX-453297","10DKX-368801","10DKX-284193","10DKX-300705","10DKX-180321","10DKX-463361",
                "10DKX-570961","10DKX-274225","10DKX-340673","10DKX-142769","10DKX-172449");

        for (DeviceFeeder deviceFeeder : feederDeviceMapper.selectList(queryWrapper)) {
            String geoList = deviceFeeder.getGeoList();
            List<List<double[]>> split = CoordinateConverter.split(geoList);


            // 杆塔、环网柜、开关站默认半径时1.5公里   变电站就默认3公里
            List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(split, 1500);
            List<List<Double>> bdz = BufferPolygonCreator.createBufferPolygon(split, 3000);
            // 判断收尾经纬度是否一致 不一致将头 添加到尾部
            if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                bufferPolygon.add(bufferPolygon.get(0));
            }
            if (!bdz.get(0).equals(bdz.get(bdz.size() - 1))) {
                bdz.add(bdz.get(0));
            }

            String s2 = BufferPolygonCreator.convertToMercator(bufferPolygon);
            String s3 = BufferPolygonCreator.convertToMercator(bdz);

            // 调用GHB.txt接口 (s2作为polygon参数)
            try {
                String ghbResult = callGHBInterface(s2);
                writeFile(deviceFeeder.getPsrId() + "_GHB_result", ghbResult);
                System.out.println("GHB接口调用成功，结果已写入文件: " + deviceFeeder.getPsrId() + "_GHB_result.json");
            } catch (Exception e) {
                System.err.println("调用GHB接口失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 调用bdz.txt接口 (s3作为polygon参数)
            try {
                String bdzResult = callBDZInterface(s3);
                writeFile(deviceFeeder.getPsrId() + "_BDZ_result", bdzResult);
                System.out.println("BDZ接口调用成功，结果已写入文件: " + deviceFeeder.getPsrId() + "_BDZ_result.json");
            } catch (Exception e) {
                System.err.println("调用BDZ接口失败: " + e.getMessage());
                e.printStackTrace();
            }

         /*   // 将数据直接写到文件中 以馈线名称_GHB 馈线名称_BDZ.json
            String b = objectMapper.writeValueAsString(bufferPolygon);
            String s = objectMapper.writeValueAsString(bdz);
            writeFile(deviceFeeder.getPsrId() + "_BDZ_buffer", s);
            writeFile(deviceFeeder.getPsrId() + "_GHB_buffer", b);
            writeFile(deviceFeeder.getPsrId() + "_GHB", s2);
            writeFile(deviceFeeder.getPsrId() + "_BDZ", s3);*/
        }

        iPlanTestService.handleFeeder();
    }

    /**
     * 调用GHB.txt对应的接口
     * @param polygon 多边形参数
     * @return 接口响应结果
     * @throws IOException
     */
    private String callGHBInterface(String polygon) throws IOException {
        String url = "http://pms.kjyzt.js.sgcc.com.cn:32080/erccachesvr/geoCenter/spatialAnalysis/queryPSRByPolygon";

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json, text/plain, */*");
        headers.put("Accept-Encoding", "deflate, gzip");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9");
        headers.put("Cache-Control", "no-cache");
        headers.put("Connection", "keep-alive");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("Cookie", "auth_token=96dcf9b3d5a4098713055f0544e1998da06c33c5d5111b8689f5ff23a972121f23a983df7b8b4de80448f43f34895d2c51a05490d2b5bb1904e1249a0bb376aa567a97ed32c56dd7306dcd82ad5b485a3b668de655dc9972877527cbbb0416d17285eaff0b34151ae35c2a5adc4273f5; _at=eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMzk1MzIzODkiLCJpYXQiOjE3NTM1MTM0NDksInN1YiI6Inh1cmIiLCJleHAiOjE3NTM1OTk4NDl9.n4J-miMXCZ2hHOeKVxsZiid4d4qLxr-11YymIdvmKBk; SESSION=89a191d0-8774-4802-86ed-53e10eddefc4");
        headers.put("Origin", "http://pms.kjyzt.js.sgcc.com.cn:32080");
        headers.put("Pragma", "no-cache");
        headers.put("Referer", "http://pms.kjyzt.js.sgcc.com.cn:32080/pms-amap-ui/?2D3C3641=x0u4r2b092551067a2b6619580341fb0&D5113FAF=103992503225308792b611b590041123&3BD058E2=273E7885323F4832C5E01F0074C2E00F&744B4F81=69D2A05BBBDD4286&jsIdsDomain=");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36");

        // 构建请求体 - GHB.txt的参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygon);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // GHB.txt中的psrQueryList
        Map<String, Object> query1 = new HashMap<>();
        query1.put("psrType", "zf04");
        query1.put("whereClause", "1=1");
        query1.put("attrNameList", null);
        query1.put("distribution", 0);
        psrQueryList.add(query1);

        Map<String, Object> query2 = new HashMap<>();
        query2.put("psrType", "zf07");
        query2.put("whereClause", "1=1");
        query2.put("attrNameList", null);
        query2.put("distribution", 0);
        psrQueryList.add(query2);

        Map<String, Object> query3 = new HashMap<>();
        query3.put("psrType", "wlgt");
        query3.put("whereClause", "1=1");
        query3.put("attrNameList", null);
        query3.put("distribution", 0);
        psrQueryList.add(query3);

        Map<String, Object> query4 = new HashMap<>();
        query4.put("psrType", "0103");
        query4.put("whereClause", "1=1");
        query4.put("attrNameList", null);
        query4.put("distribution", 0);
        psrQueryList.add(query4);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 设置attrNameList
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("classId");
        attrNameList.add("psrId");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("psrName");
        attrNameList.add("zoneId");
        attrNameList.add("ZoneName");
        attrNameList.add("innerTransId");
        attrNameList.add("srcId");
        attrNameList.add("srcName");
        attrNameList.add("portList");
        attrNameList.add("portNameList");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("VOLTAGE_LE");
        attrNameList.add("coordinate");
        attrNameList.add("feederId");
        attrNameList.add("feederName");
        attrNameList.add("useNature");
        attrNameList.add("chargedState");
        attrNameList.add("switchStatus");
        attrNameList.add("distribution");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("crossFeederId");
        attrNameList.add("crossFeederProvince");
        attrNameList.add("isProvinceContainer");
        attrNameList.add("direction");
        attrNameList.add("dataSource");
        attrNameList.add("plantType");
        attrNameList.add("pubPrivFlag");
        attrNameList.add("siteName");
        attrNameList.add("zoneName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);

        return HttpUtils.postJson(url, headers, requestBody);
    }

    /**
     * 调用bdz.txt对应的接口
     * @param polygon 多边形参数
     * @return 接口响应结果
     * @throws IOException
     */
    private String callBDZInterface(String polygon) throws IOException {
        String url = "http://pms.kjyzt.js.sgcc.com.cn:32080/erccachesvr/geoCenter/spatialAnalysis/queryPSRByPolygon";

        // 构建请求头 (与GHB相同)
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json, text/plain, */*");
        headers.put("Accept-Encoding", "deflate, gzip");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9");
        headers.put("Cache-Control", "no-cache");
        headers.put("Connection", "keep-alive");
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("Cookie", "auth_token=96dcf9b3d5a4098713055f0544e1998da06c33c5d5111b8689f5ff23a972121f23a983df7b8b4de80448f43f34895d2c51a05490d2b5bb1904e1249a0bb376aa567a97ed32c56dd7306dcd82ad5b485a3b668de655dc9972877527cbbb0416d17285eaff0b34151ae35c2a5adc4273f5; _at=eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxMzk1MzIzODkiLCJpYXQiOjE3NTM1MTM0NDksInN1YiI6Inh1cmIiLCJleHAiOjE3NTM1OTk4NDl9.n4J-miMXCZ2hHOeKVxsZiid4d4qLxr-11YymIdvmKBk; SESSION=89a191d0-8774-4802-86ed-53e10eddefc4");
        headers.put("Origin", "http://pms.kjyzt.js.sgcc.com.cn:32080");
        headers.put("Pragma", "no-cache");
        headers.put("Referer", "http://pms.kjyzt.js.sgcc.com.cn:32080/pms-amap-ui/?2D3C3641=x0u4r2b092551067a2b6619580341fb0&D5113FAF=103992503225308792b611b590041123&3BD058E2=273E7885323F4832C5E01F0074C2E00F&744B4F81=69D2A05BBBDD4286&jsIdsDomain=");
        headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36");

        // 构建请求体 - bdz.txt的参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("polygon", polygon);
        requestBody.put("srs", "EPSG:3857");

        Map<String, Object> psrQueryInfo = new HashMap<>();
        List<Map<String, Object>> psrQueryList = new ArrayList<>();

        // bdz.txt中的psrQueryList
        Map<String, Object> query1 = new HashMap<>();
        query1.put("psrType", "zf01");
        query1.put("whereClause", "1=1");
        query1.put("attrNameList", null);
        query1.put("distribution", 1);
        psrQueryList.add(query1);

        Map<String, Object> query2 = new HashMap<>();
        query2.put("psrType", "zf04");
        query2.put("whereClause", "1=1");
        query2.put("attrNameList", null);
        query2.put("distribution", 0);
        psrQueryList.add(query2);

        Map<String, Object> query3 = new HashMap<>();
        query3.put("psrType", "zf07");
        query3.put("whereClause", "1=1");
        query3.put("attrNameList", null);
        query3.put("distribution", 0);
        psrQueryList.add(query3);

        psrQueryInfo.put("psrQueryList", psrQueryList);

        // 设置attrNameList (与GHB相同)
        List<String> attrNameList = new ArrayList<>();
        attrNameList.add("classId");
        attrNameList.add("psrId");
        attrNameList.add("psrType");
        attrNameList.add("psrTypeName");
        attrNameList.add("psrName");
        attrNameList.add("zoneId");
        attrNameList.add("ZoneName");
        attrNameList.add("innerTransId");
        attrNameList.add("srcId");
        attrNameList.add("srcName");
        attrNameList.add("portList");
        attrNameList.add("portNameList");
        attrNameList.add("vlevelName");
        attrNameList.add("vlevelCode");
        attrNameList.add("VOLTAGE_LE");
        attrNameList.add("coordinate");
        attrNameList.add("feederId");
        attrNameList.add("feederName");
        attrNameList.add("useNature");
        attrNameList.add("chargedState");
        attrNameList.add("switchStatus");
        attrNameList.add("distribution");
        attrNameList.add("maintCrew");
        attrNameList.add("maintCrewName");
        attrNameList.add("maintOrg");
        attrNameList.add("maintOrgName");
        attrNameList.add("cityOrg");
        attrNameList.add("cityOrgName");
        attrNameList.add("provinceId");
        attrNameList.add("crossFeederId");
        attrNameList.add("crossFeederProvince");
        attrNameList.add("isProvinceContainer");
        attrNameList.add("direction");
        attrNameList.add("dataSource");
        attrNameList.add("plantType");
        attrNameList.add("pubPrivFlag");
        attrNameList.add("siteName");
        attrNameList.add("zoneName");

        psrQueryInfo.put("attrNameList", attrNameList);
        requestBody.put("psrQueryInfo", psrQueryInfo);

        return HttpUtils.postJson(url, headers, requestBody);
    }

    /**
     * 写入文件
     * @param fileName
     * @param content
     * @throws Exception
     */
    private void writeFile(String fileName, String content) throws Exception {
        Files.write(Paths.get("C:\\Users\\<USER>\\Desktop\\temp\\data\\" + fileName + ".json"), content.getBytes());
        System.out.println("写入文件成功");
    }

}
