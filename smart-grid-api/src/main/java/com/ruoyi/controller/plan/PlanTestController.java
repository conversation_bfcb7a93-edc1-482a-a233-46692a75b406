package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.QueryDevBo;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.plan.TestService;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.text.TrendTextCalcServiceImpl;
import com.ruoyi.util.BufferPolygonCreator;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 方案
 */
@RestController
@RequestMapping("/planTest")
@SaIgnore
public class PlanTestController {

    @Autowired
    IPlanTestService iPlanTestService;


    @Autowired
    TrendTextCalcServiceImpl textTrendCalcService;

    @Autowired
    TestService testService;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 用于测试线段
     */
    @GetMapping("/getSegList")
    public R<ArrayList<SegBetweenVo>> getSegList(@RequestParam String feederId, @RequestParam(required = false) String deviceId) {

        ArrayList<SegBetween> segBetweenList = iPlanTestService.getSegBetweenList(feederId, deviceId);
        return R.ok(NodeUtils.toSegBetweenVoList(segBetweenList));
    }

    /**
     * 用于测试线段
     */
    @PostMapping("/generatePlan")
    public R<List<Plan>> getSegList(@RequestBody GeneratePlanBo generatePlanBo) {

        List<Plan> plans = iPlanTestService.generateGridPlan(generatePlanBo);
        return R.ok(plans);
    }

    /**
     * 用于测试线段
     */
    @GetMapping("/getQueryDevs")
    public R<List<QueryDevBo>> getSegList() {
        return R.ok(testService.query());
    }

    /**
     * 获取联络开关
     */
    @GetMapping("/getContactKgs")
    public R<ArrayList<NodeVo>> getContactKgs(@RequestParam String feederId) {

        ArrayList<Node> result = iPlanTestService.getContactKgs(feederId);
        return R.ok(NodeUtils.toNodeVos(result));
    }

    /**
     * 获取联络开关对应各个主干路径都主干开关的各个分段
     */
    @GetMapping("/getAllContactKgSegList")
    public R<List<HashMap<String, Object>>> getAllContactKgSegList(@RequestParam String feederId) {
        return R.ok(iPlanTestService.getAllContactKgSegList(feederId));
    }

    /**
     * 获取主干路径
     */
    @GetMapping("/getMainPath")
    public R<List<NodeVo>> getMainPath(@RequestParam String feederId) {
        return R.ok(NodeUtils.toNodeVos(iPlanTestService.getMainPath(feederId)));
    }

    @Resource
    private FeederDeviceMapper feederDeviceMapper;
    /**
     * 测试方法
     * 获取指定的feederId的地理位置，然后将地理位置转换为魔卡托
     * 获取10DKX-53969
     * 10DKX-453297
     * 10DKX-368801
     * 10DKX-284193
     * 10DKX-300705
     * 10DKX-180321
     * 10DKX-463361
     * 10DKX-463361
     * 10DKX-570961
     * 10DKX-274225
     * 10DKX-340673
     * 10DKX-142769
     * 10DKX-172449
     * 这些指定路线
     */
    @GetMapping("/test")
    public void test() throws Exception {
        LambdaQueryWrapper<DeviceFeeder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceFeeder::getPsrId, "10DKX-53969","10DKX-453297","10DKX-368801","10DKX-284193","10DKX-300705","10DKX-180321","10DKX-463361",
                "10DKX-570961","10DKX-274225","10DKX-340673","10DKX-142769","10DKX-172449");

        for (DeviceFeeder deviceFeeder : feederDeviceMapper.selectList(queryWrapper)) {
            String geoList = deviceFeeder.getGeoList();
            List<List<double[]>> split = CoordinateConverter.split(geoList);


            // 杆塔、环网柜、开关站默认半径时1.5公里   变电站就默认3公里
            List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(split, 1500);
            List<List<Double>> bdz = BufferPolygonCreator.createBufferPolygon(split, 3000);
            // 判断收尾经纬度是否一致 不一致将头 添加到尾部
            if (!bufferPolygon.get(0).equals(bufferPolygon.get(bufferPolygon.size() - 1))) {
                bufferPolygon.add(bufferPolygon.get(0));
            }
            if (!bdz.get(0).equals(bdz.get(bdz.size() - 1))) {
                bdz.add(bdz.get(0));
            }

            String s2 = BufferPolygonCreator.convertToMercator(bufferPolygon);
            String s3 = BufferPolygonCreator.convertToMercator(bdz);
            // 将数据直接写到文件中 以馈线名称_GHB 馈线名称_BDZ.json
            String b = objectMapper.writeValueAsString(bufferPolygon);
            String s = objectMapper.writeValueAsString(bdz);
            writeFile(deviceFeeder.getPsrId() + "_BDZ_buffer", s);
            writeFile(deviceFeeder.getPsrId() + "_GHB_buffer", b);
            writeFile(deviceFeeder.getPsrId() + "_GHB", s2);
            writeFile(deviceFeeder.getPsrId() + "_BDZ", s3);
        }

        iPlanTestService.handleFeeder();
    }

    /**
     * 写入文件
     * @param fileName
     * @param content
     * @throws Exception
     */
    private void writeFile(String fileName, String content) throws Exception {
        Files.write(Paths.get("C:\\Users\\<USER>\\Desktop\\temp\\data\\" + fileName + ".json"), content.getBytes());
        System.out.println("写入文件成功");
    }

}
