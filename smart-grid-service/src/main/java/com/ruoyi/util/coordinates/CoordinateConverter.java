package com.ruoyi.util.coordinates;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.util.BufferPolygonCreator;
import org.locationtech.jts.geom.*;
import org.locationtech.proj4j.ProjCoordinate;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CoordinateConverter {

    static GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 将经纬度字符串转换为double数组 [lon, lat]
     *
     * @param lon 经度字符串
     * @param lat 纬度字符串
     * @return double数组 [lon, lat]，如果解析失败则返回null
     */
    public static double[] convert(String lon, String lat) {
        if (lon == null || lat == null) {
            return null;
        }

        try {
            double longitude = parseCoordinate(lon);
            double latitude = parseCoordinate(lat);

            // 验证经纬度范围
            if (!isValidLongitude(longitude) || !isValidLatitude(latitude)) {
                throw new IllegalArgumentException("Coordinate out of range");
            }

            return new double[]{longitude, latitude};
        } catch (IllegalArgumentException e) {
            System.err.println("Failed to convert coordinates: " + e.getMessage());
            return null;
        }
    }

    /**
     * 解析单个坐标字符串
     */
    private static double parseCoordinate(String coord) {
        // 去除空格并替换逗号为小数点
        String cleaned = coord.trim().replace(',', '.');

        // 验证是否为合法数字格式
        if (!cleaned.matches("[-+]?[0-9]*\\.?[0-9]+")) {
            throw new NumberFormatException("Invalid coordinate format: " + cleaned);
        }

        return Double.parseDouble(cleaned);
    }

    // 辅助方法：解析坐标字符串 x,y 为double[]

    /**
     * 将经纬度112,32 转为[112,32]
     */
    public static Double[] parseLngLatCoords(String coordString) {
        if (coordString == null || coordString.trim().isEmpty()) {
            throw new IllegalArgumentException("坐标字符串不能为空");
        }

        String[] parts = coordString.trim().split(",");
        if (parts.length < 2) {
            throw new IllegalArgumentException("需要至少两个坐标值");
        }

        return new Double[]{
                Double.parseDouble(parts[0].trim()),
                Double.parseDouble(parts[1].trim())
        };
    }

    /**
     * 将字符串(118.75496647568387 32.019883614089096 118.75496739045269 32.01988352622447)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<List<Double>> parseCoordinates(String coordinateStr) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 按空格分割字符串
        String[] tokens = coordinateStr.trim().split("\\s+");

        // 检查是否有偶数个数字（确保可以两两分组）
        if (tokens.length % 2 != 0) {
            throw new IllegalArgumentException("坐标点数量必须为偶数（经度和纬度成对）");
        }

        List<List<Double>> result = new ArrayList<>();
        for (int i = 0; i < tokens.length; i += 2) {
            // 提取经度和纬度
            double longitude = Double.parseDouble(tokens[i]);
            double latitude = Double.parseDouble(tokens[i + 1]);

            // 添加到结果列表
            result.add(Arrays.asList(longitude, latitude));
        }

        return result;
    }

    /**
     * 将字符串[[118.837035324425,31.9339936898715],[118.840276362526,31.9273197976144],[118.83709637547,31.922892588189],[118.818772698846,31.9158938571851],[118.812384951023,31.9151657651413],[118.800061221151,31.9090838980235],[118.798175294444,31.9314032602619],[118.79040969269,31.9544800175526],[118.790549974984,31.9544601542134],[118.79082052315,31.9544204408566],[118.790970706615,31.9544006352857],[118.792203093223,31.9542420261108],[118.798274857511,31.9535189230338],[118.803504964702,31.9528748290018],[118.80495796929,31.9527968143383],[118.806070687945,31.952808615216],[118.809959736638,31.9528449868007],[118.814350774662,31.9528223317898],[118.81958387316,31.952791120581],[118.823342147827,31.952823549658],[118.823337977809,31.9528460491014],[118.823331873824,31.9528789017262],[118.828163492552,31.9522590359993],[118.83350372276,31.941265905828],[118.837035324425,31.9339936898715]]坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<List<Double>> parseCoordinates2(String coordinateStr) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(coordinateStr, List.class);
    }

    /**
     * 将字符串(118.75496647568387,32.019883614089096)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<Double> parseCommaCoordinates(String coordinateStr, String split) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>(); // 或返回空列表，根据业务需求
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }

        // 按逗号分割字符串
        String[] parts = coordinateStr.split(split);

        // 转换为 Double 列表
        return Arrays.stream(parts)
                .map(String::trim) // 去除可能的空格
                .map(Double::parseDouble)
                .collect(Collectors.toList());
    }

    /**
     * 将二维数组的坐标coords转为LineString
     *
     * @param coords [[110,39],[]]
     * @return
     */
    public static LineString toLineString(List<List<Double>> coords) {

        Coordinate[] coordsList = coords.stream().map(lngLat -> new Coordinate(lngLat.get(0), lngLat.get(1))).toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordsList);
    }

    /**
     * 将二维坐标数组转换为LineString
     *
     * @param coords 二维double数组 [[x1,y1], [x2,y2], ...]
     * @return LineString几何对象
     */
    public static LineString toLineString(double[][] coords) {
        Coordinate[] coordinates = Arrays.stream(coords)
                .map(arr -> new Coordinate(arr[0], arr[1]))
                .toArray(Coordinate[]::new);
        return geometryFactory.createLineString(coordinates);
    }

    /**
     * 将 List<List<double[]>> 转换为单个 LineString
     * 假设所有内部列表的点都属于同一条连续的线
     */
    public static LineString convertToSingleLineString(List<List<double[]>> coordinatesList) {
        if (coordinatesList == null || coordinatesList.isEmpty()) {
            return null;
        }

        // 计算所有点的总数
        int totalPoints = 0;
        for (List<double[]> line : coordinatesList) {
            if (line != null) {
                totalPoints += line.size();
            }
        }

        // 至少需要两个点才能构成 LineString
        if (totalPoints < 2) {
            return null;
        }

        // 创建 Coordinate 数组
        Coordinate[] coords = new Coordinate[totalPoints];
        int index = 0;

        // 遍历所有内部列表，将点添加到 Coordinate 数组中
        for (List<double[]> line : coordinatesList) {
            if (line == null) {
                continue;
            }

            for (double[] point : line) {
                if (point != null && point.length >= 2) {
                    coords[index++] = new Coordinate(point[0], point[1]);
                } else {
                    // 处理无效点：设置为 (0, 0) 或跳过
                    coords[index++] = new Coordinate(0, 0);
                }
            }
        }

        // 创建 LineString
        return geometryFactory.createLineString(coords);
    }


    /**
     * 将FastJSON的JSONArray转换为LineString
     *
     * @param jsonArray 坐标JSON数组，支持格式：
     *                  - [[x1,y1], [x2,y2], ...]
     *                  - [x1,y1, x2,y2, ...] (平面数组)
     * @return LineString几何对象
     * @throws IllegalArgumentException 当输入格式无效时抛出
     */
    public static LineString jsonArrayToLineString(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            throw new IllegalArgumentException("坐标数组不能为空");
        }

        try {
            // 情况1：嵌套数组格式 [[x1,y1], [x2,y2], ...]
            if (jsonArray.get(0) instanceof JSONArray) {
                Coordinate[] coordinates = new Coordinate[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONArray point = jsonArray.getJSONArray(i);
                    coordinates[i] = new Coordinate(
                            point.getDoubleValue(0),
                            point.getDoubleValue(1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
            // 情况2：平面数组格式 [x1,y1, x2,y2, ...]
            else if (jsonArray.get(0) instanceof Number) {
                if (jsonArray.size() % 2 != 0) {
                    throw new IllegalArgumentException("平面坐标数组长度必须是偶数");
                }
                Coordinate[] coordinates = new Coordinate[jsonArray.size() / 2];
                for (int i = 0; i < coordinates.length; i++) {
                    coordinates[i] = new Coordinate(
                            jsonArray.getDoubleValue(i * 2),
                            jsonArray.getDoubleValue(i * 2 + 1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON坐标转换失败: " + e.getMessage(), e);
        }

        throw new IllegalArgumentException("不支持的JSON坐标格式");
    }

    /**
     * Point转为数组的坐标系
     */
    public static List<Double> pointToLngLat(Point point) {
        return Arrays.asList(point.getX(), point.getY());
    }

    /**
     * List<Double> 类型转为double类型
     */
    public static double[] listToArr(List<Double> doubles) {
        return doubles.stream().mapToDouble(Double::doubleValue).toArray();
    }


    /**
     * 将三维字符串坐标，解析成List<List<double[]>>的格式坐标，
     *
     * @param geoList 三维坐标字符串
     * @return
     */
    public static List<List<double[]>> split(String geoList) {
        // 1. 按逗号分割为子串
        String[] lineStrings = geoList.split(",");

        List<List<double[]>> groupedCoordinates = new ArrayList<>();

        for (String line : lineStrings) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            // 2. 按空格分割为坐标对
            String[] points = trimmedLine.split(" ");
            List<double[]> coordinates = new ArrayList<>();

            for (int i = 0; i < points.length; i += 2) {
                if (i + 1 >= points.length) {
                    throw new IllegalArgumentException("坐标点数为奇数，无法配对: " + trimmedLine);
                }
                double x = Double.parseDouble(points[i]);
                double y = Double.parseDouble(points[i + 1]);
                coordinates.add(new double[]{x, y});
            }

            // 3. 将坐标按两个一组进行分组

            groupedCoordinates.add(coordinates);
        }
        return groupedCoordinates;
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(double lng, double lat) {
        return geometryFactory.createPoint(new Coordinate(lng, lat));
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(List<Double> coords) {
        return geometryFactory.createPoint(new Coordinate(coords.get(0), coords.get(1)));
    }


    /**
     * 计算两个经纬度坐标的中点
     *
     * @param lon1 第一个点经度
     * @param lat1 第一个点纬度
     * @param lon2 第二个点经度
     * @param lat2 第二个点纬度
     * @return 中点坐标 [经度, 纬度]
     */
    public static List<Double> getMidpoint(double lon1, double lat1, double lon2, double lat2) {
        // 1. 转换为弧度
        double lon1Rad = Math.toRadians(lon1);
        double lat1Rad = Math.toRadians(lat1);
        double lon2Rad = Math.toRadians(lon2);
        double lat2Rad = Math.toRadians(lat2);

        // 2. 计算半差
        double dLon = (lon2Rad - lon1Rad) / 2;
        double dLat = (lat2Rad - lat1Rad) / 2;

        // 3. 球面中点公式（使用半正矢公式）
        double a = Math.sin(dLat) * Math.sin(dLat) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(dLon) * Math.sin(dLon);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double midLat = Math.atan2(
                Math.sin(lat1Rad) + Math.sin(lat2Rad) * Math.cos(c),
                Math.sqrt((Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) *
                        (Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) +
                        Math.sin(lat2Rad) * Math.sin(lat2Rad) * Math.sin(c) * Math.sin(c))
        );
        double midLon = lon1Rad + Math.atan2(
                Math.sin(dLon) * Math.cos(lat2Rad),
                Math.cos(dLat) * Math.cos(lat1Rad) - Math.sin(dLat) * Math.sin(lat2Rad) * Math.cos(dLon)
        );

        // 4. 转换为角度
        double midLonDeg = Math.toDegrees(midLon);
        double midLatDeg = Math.toDegrees(midLat);

        return Arrays.asList(midLonDeg, midLatDeg);
    }


    /**
     * 验证经度范围
     */
    private static boolean isValidLongitude(double lon) {
        return lon >= -180 && lon <= 180;
    }

    /**
     * 验证纬度范围
     */
    private static boolean isValidLatitude(double lat) {
        return lat >= -90 && lat <= 90;
    }


    /**
     * 将Geometry坐标转换成List<Double>，方法总入口下面包含，点，线，面
     *
     * @param geometry
     * @return
     */
    public static List<Double> convertGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        if (geometry instanceof Point) {
            return pointToDoubleList((Point) geometry);
        } else if (geometry instanceof LineString) {
            return geometryToDoubleList(geometry);
        } else if (geometry.getNumGeometries() > 1) {
            return multiGeometryToDoubleList(geometry);
        }

        return Collections.emptyList();
    }

    /**
     * 将Geometry坐标转换成List<Double>，面转换
     *
     * @param geometry
     * @return
     */
    public static List<Double> multiGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        List<Double> result = new ArrayList<>();
        for (int i = 0; i < geometry.getNumGeometries(); i++) {
            Geometry subGeom = geometry.getGeometryN(i);
            result.addAll(geometryToDoubleList(subGeom));
        }

        return result;
    }

    /**
     * 将Geometry坐标转换成List<Double>，线转换
     *
     * @param geometry
     * @return
     */
    public static List<Double> geometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        Coordinate[] coordinates = geometry.getCoordinates();
        List<Double> result = new ArrayList<>(coordinates.length * 2);

        for (Coordinate coord : coordinates) {
            result.add(coord.x);
            result.add(coord.y);
        }

        return result;
    }

    /**
     * 将Point点坐标转换成List<Double>，点坐标转换转换
     *
     * @param point
     * @return
     */
    public static List<Double> pointToDoubleList(Point point) {
        if (point == null || point.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(point.getX(), point.getY());
    }


    /**
     * 将经纬度转换为JSON数组格式
     */
    public static String convertToJson(double lon, double lat) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();

        // 创建JSON数组节点
        ArrayNode arrayNode = objectMapper.createArrayNode();
        arrayNode.add(lon);
        arrayNode.add(lat);

        // 转换为JSON字符串
        return objectMapper.writeValueAsString(arrayNode);
    }




    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static String convertToJson(List<double[]> list) {
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert list to JSON", e);
        }
    }
    public static void main(String[] args) throws JsonProcessingException {
        List<List<double[]>> split = split("118.98309818469966 31.558630276681566 118.98309822217736 31.558657093487827, 118.99163903725616 31.587707910038308 118.99143385553515 31.587872997802496 118.99146117111016 31.587873097056743, 118.97924312068524 31.56428269291794 118.97921634951985 31.564282645642226, 118.97647841473706 31.563025082422342 118.97648509500299 31.563030238771052 118.9768706697231 31.563379823727452 118.97718804934057 31.56362047514417 118.97758700177549 31.564011247230702 118.97776784322078 31.564147210020074 118.97800589334763 31.56431452644417 118.97844674922611 31.56385644603674 118.97869736403314 31.563297962951026 118.978987394272 31.562875127878595 118.97928095051783 31.56239898534369 118.9795266526919 31.561976061993665 118.97970615739911 31.561679433255534 118.97996845136217 31.561439576667475 118.9802268297656 31.560746700528004 118.98075698308546 31.559969967345452 118.98090970634514 31.559921709829364 118.98091542219865 31.559914526223725 118.98089537643233 31.55990538459873 118.98095611720066 31.559829308751283 118.98104478364918 31.559746484134195 118.98119352014791 31.55963452509286 118.98130259938746 31.559567377707396 118.98148751871074 31.559471624078967 118.98146977812111 31.559446322805456 118.98147991528663 31.559457081353155 118.98183481263949 31.559306226880366 118.98189155316675 31.559271997761034 118.9819686840918 31.559259297510426 118.98204368625755 31.559213209489588 118.9822181969669 31.5591252538134 118.98243085857929 31.559030513737977 118.9825899421897 31.558918733157007 118.9828519640153 31.558777748661626 118.98309818469966 31.558630276681566 118.98267865889268 31.558355568233807 118.9823092332448 31.558149074316464 118.98187083136875 31.557866228656266 118.98151198811928 31.55759947078445 118.98118724933593 31.557432903701123 118.98081356399106 31.557183329653018 118.98045039637488 31.556877867358242 118.98011849180236 31.556618275043054 118.98001376247403 31.55651156656067 118.97914629179907 31.555996575777584 118.97911862403507 31.55590817194043 118.97915611126457 31.555832206210223 118.97918294880022 31.555832298569435, 118.94326568558276 31.56916762510045 118.94326567997007 31.569128439301682 118.94305643245343 31.56932248689128 118.94301147552122 31.569320164249778 118.9430602765197 31.56932657522118 118.94306027922542 31.569345472271074, 118.94474455775642 31.570732073308363 118.94517914721835 31.570748883799787 118.94521346312541 31.570494361306498 118.94525594275201 31.57050032866248 118.94522920665146 31.570500313081247, 118.94288785865767 31.572301346848022 118.9432189672537 31.57144497598201 118.94321905489333 31.571444975097283 118.94322061169797 31.57139423563148 118.94329709144505 31.571381571753417 118.9432970947697 31.57140491877589, 118.991919823051 31.59907948173174 118.99203781591468 31.598882770059106, 118.97941689794081 31.580434381981647 118.97942582081579 31.5804354875924 118.97944799420866 31.580438779360875 118.97979969683975 31.58087331137064 118.98006311610979 31.581327118775985 118.98030339445648 31.581745874570224 118.98055505635506 31.58218607706358 118.98075253786389 31.58256101214409 118.98084675767056 31.582999112321005 118.9812072347139 31.58333269452329 118.98051358955293 31.58397551638281 118.97972574515131 31.58415914577299 118.97971697336035 31.584160882317555, 118.94685309791377 31.55381548420438 118.94682629355383 31.55381542145799 118.9468473244091 31.553817987589582 118.9470243787485 31.55385431682655 118.94727010351788 31.554480173393564 118.94742535618818 31.55495146911322 118.94757970259042 31.55533902008005 118.94784894778 31.555864697877414 118.94803297080199 31.556230060520384 118.94826628478789 31.55669016937445 118.94846818282876 31.55709838949749 118.94868450650516 31.557513103883565 118.94889500103076 31.557923410574574 118.94911144293623 31.558338107974706 118.94932398733555 31.55875146852819 118.94953725631508 31.559161502301883 118.9497529610136 31.559589344284078 118.95003874485863 31.56002355815153 118.95028713077207 31.560392725118625 118.95058256746847 31.560834657924566 118.95095924616513 31.561220197096645 118.95130352479174 31.561594654757958 118.95164832053983 31.56197259851007 118.95184523515063 31.562279299182006 118.95222010572459 31.5626276151293 118.95256043334767 31.56299315028207 118.95288247612966 31.56324754403997 118.95312649300318 31.563612448563045 118.95337254613915 31.563901142510808 118.95352610977187 31.564196986621187 118.95363323690584 31.564524826115512 118.95391804983734 31.564746889332767 118.95403202777158 31.565022541359625 118.95403022333015 31.565360550142394 118.95402687597024 31.565365885303414 118.9540269057952 31.565343141889535, 118.96467625697638 31.573599912279086 118.96467410742198 31.573618976505426, 118.99541137248214 31.567201956518154 118.99520993114837 31.56760246131211 118.99519320893208 31.567573709697793 118.99519321237622 31.567595666170895, 118.94210623120779 31.56791460026076 118.94210619477123 31.56789181232616 118.94208119797972 31.567916723186915 118.94221224142413 31.568004670861846 118.94254006273057 31.567778483937794 118.94271328729408 31.567663171738495 118.94286850945221 31.56746792723027 118.94322834633093 31.5664735797916 118.94339582209523 31.566193950328117 118.94373494040538 31.56577118574759 118.94389848691615 31.5655337783491 118.94421396687439 31.565322807692723 118.94447983833494 31.565056847285888 118.94470009243086 31.56472772328868 118.94492125100359 31.564380674796645 118.94550093731057 31.563525075100927 118.94616026832425 31.562768390968973 118.94642811522773 31.562354620259935, 118.97228061767714 31.559018014032233 118.972280619463 31.559029497425595, 118.96457125730994 31.55007407965176 118.9645711088777 31.5501017464705, 118.95349213130349 31.574037997986768 118.95344091936963 31.57408883035016 118.95344123076883 31.57411644024776, 118.95375013074725 31.57507031794536 118.95375012523517 31.57503209141751 118.95368814033613 31.574926264147212 118.95358545669177 31.574584716918327 118.95358536839636 31.574584719265577 118.95363051075341 31.57447533097827, 118.98750364109165 31.58660369329975 118.98750363893387 31.586588991698413 118.98750430841682 31.586588993539547 118.98750430841682 31.586588993539547 118.98750277428745 31.586634689198203 118.98749057370703 31.58663830827036 118.98829151779309 31.58697452637769 118.98834560196428 31.58692875706197 118.98834560196428 31.58692875706197, 118.98517378054531 31.595064203372953 118.9848986199891 31.59500588811691, 118.9639029501188 31.57556089231204 118.9641790168829 31.575618034364236, 118.99225221084978 31.59833594353666 118.99225823117287 31.598330264964257 118.99227160826155 31.59831488790753 118.99225001184583 31.59783626636948 118.99229459526215 31.59727844806865 118.99231545850077 31.596836331683612 118.9924093826036 31.596191497914127 118.99246132416081 31.595750817477366 118.99219836497645 31.595614731506817 118.99204685131983 31.59562667991641 118.99192261241969 31.59566654226364 118.99184689590552 31.595716924298937 118.99189431866108 31.595925043302806 118.9918540312328 31.595927816431512 118.99185406772506 31.59595059618529, 118.98563357912364 31.582538629149113 118.98563569170967 31.582531194724627 118.98566501030496 31.58240924188373 118.98666827022666 31.582411145740686 118.98669984118582 31.582412063922522 118.98670885196637 31.582412319849674, 118.94768313918286 31.56724690294825 118.9476831026879 31.56722411500513 118.94767189460484 31.567246585913715 118.94748858210262 31.567739313524207 118.9472882536503 31.5682737522474 118.94710375109412 31.568839085628355 118.94648898159042 31.56868358535285 118.94577125719694 31.568449127603593 118.94572437721335 31.56851388616479 118.94570111459976 31.568556319684305 118.9454222644336 31.569070927042336 118.9450881731874 31.56972306255316 118.94474455775642 31.570732073308363 118.944637079268 31.571267410861857 118.94459928750693 31.571781039300095 118.94455320198439 31.572125875978525 118.94449824195037 31.57261999297619 118.9445386030432 31.572852656045153 118.944177013855 31.57274170962981 118.9437167792012 31.572656829835736 118.94345000554016 31.572593909565608 118.94304840980175 31.572462578807986 118.94298002742728 31.572518072070814 118.94297112368194 31.572525452540393 118.94246859796961 31.5729968842336 118.94241033696288 31.573033125300906 118.94198968764196 31.573713793580907 118.94196867799683 31.57400309676265 118.94192885040447 31.57429146051505 118.94192216197196 31.574665381142783 118.94187865326771 31.57531305812592 118.94191689705299 31.57577528940084 118.94194274224668 31.576148351637524 118.94200021525751 31.57658029232815 118.94198399453921 31.57684365562629 118.94198529196251 31.577152245122175 118.94180175592263 31.577704576937226 118.94163451549626 31.578187972757615 118.94148774488775 31.578388063180505 118.94128110931172 31.578825394070144 118.94108967650465 31.579236745326657 118.94096531667958 31.5795825738174 118.94113014248566 31.57992654117935 118.94113492444251 31.580360006640152 118.94081647454438 31.580859822333643 118.94069268249679 31.581186429079526 118.94049483780178 31.58178636527338 118.94065341907965 31.582316191369234, 118.94254891303876 31.568872606349277 118.94254894947174 31.568895394034215, 118.99357244790237 31.569785459273913 118.99355006477268 31.56978288557433, 118.99240925598157 31.58250729379352 118.9928465715025 31.582571569114137 118.99273320015295 31.58352212092754 118.99275491201209 31.583527457253993 118.99276368693724 31.583529240445714, 118.98750840261 31.568600851198905 118.98748583662642 31.568601155887137 118.9868387025361 31.568621690741303 118.98631732330867 31.56863000519117 118.9860536455098 31.568623347370714 118.98462804879003 31.568717436723222 118.98453923490997 31.568719300824515 118.98398853821193 31.56875688114302 118.98359415419351 31.568783927687413 118.98296802464861 31.568861406007215 118.9824516585945 31.568874029956596 118.98205904653712 31.56890391878768 118.98138419190049 31.56896151688419 118.98076167160224 31.56901425615278 118.98013961229513 31.569072563418104 118.9796455791414 31.56909117664105, 118.96841968553042 31.548304078207558 118.9683872452365 31.548304170659208, 118.96591987829581 31.571461630603867 118.96594670458903 31.571461712221172, 118.97703044535336 31.587651135321195 118.97703044700896 31.587662615106414, 118.9867223763015 31.582412241304553 118.98670885196637 31.582412319849674, 118.9774765724175 31.578700660664964 118.97760636217038 31.5787410564133, 118.98539375782178 31.595377486026752 118.9853937532413 31.59534520638539 118.98540009179075 31.59534522349586 118.98540009179075 31.59534522349586 118.98534688715374 31.595254369600713 118.98554224104772 31.595399244183977 118.98485182338804 31.595142892945354 118.98487251081019 31.595120647249782 118.98487251081019 31.595120647249782, 118.98491214179997 31.59500592449034 118.98492115634264 31.595005948741335 118.98495267401825 31.595006011179184, 118.99089644562892 31.597929190644457 118.99089644357989 31.59791470638986 118.99089644357989 31.59791470638986 118.99089490719393 31.597913817611182 118.9920274960795 31.59886687280078 118.99203787899889 31.59887130330633 118.99203783415768 31.598894259477408, 118.98793295377055 31.58238010338934 118.98794849178088 31.582372235622955, 118.98522728011181 31.594873951218794 118.98525886447385 31.594874058764493 118.98526787908392 31.594874083080573, 118.96068818930648 31.571869626058458 118.96080328964524 31.57228538269538 118.96086066465348 31.57258269849192 118.96105183847355 31.573259681498858 118.9610908506822 31.57332172021925 118.9611508888997 31.573624202287398 118.9610730491329 31.573658861912133 118.96127031717329 31.574155592110138 118.96134649618057 31.57453702458833 118.96147931988284 31.57500369073552 118.96188028387745 31.5751051898009 118.9622840595513 31.57520793549973 118.9627063869961 31.57530576593421 118.96309485167107 31.575445702228915 118.96344404297017 31.575481555269572 118.96346613308468 31.575485503091265 118.96347501269054 31.57548681362404, 118.98997208868136 31.571134659531396 118.98993950360058 31.57113440763653 118.99003221707983 31.57113816171784 118.99052841860856 31.570988811640543 118.99103036534142 31.571706122081824 118.99083504618169 31.57195306200085 118.99057134200737 31.57259470140622 118.99056328936095 31.57261165930157 118.9905630646977 31.572639317822702, 118.97835472777031 31.57100234788272 118.97833682396133 31.57100044227812 118.97831897344936 31.57099891030645, 118.9935736740686 31.584799195581024 118.9935736373814 31.584776013464687 118.99356356540575 31.58477595535356 118.99365303797009 31.58408733121467 118.99348052959382 31.583964380653256 118.9935901973251 31.58341289601042 118.99372444407018 31.582797019541847 118.99384763647524 31.5825809503821 118.99387576758848 31.582422506029587 118.99393110298892 31.581764178732083 118.99393259427043 31.581728325672064 118.99396615806215 31.581242513443325 118.99394715080308 31.58081434829975 118.9940024119452 31.580290338767238 118.99406972012319 31.57955557466989 118.99418336927137 31.57882908955475 118.99422904124275 31.578162873339455 118.9943188160282 31.578156908119308 118.99431883265152 31.578156919343943 118.99481423810852 31.578130755815927 118.9948141295013 31.57813075453177 118.99491340713293 31.5781481336852 118.99493597067308 31.578147655227262 118.99494498402102 31.57814749390459, 118.96465358675317 31.55151304306911 118.9646392098549 31.551503907885603 118.96462093519706 31.551489500584836, 118.98562916273352 31.582557365474337 118.98563357912364 31.582538629149113, 118.99541137067834 31.567190474437925 118.99541137248214 31.567201956518154, 118.97980471620515 31.57053926834153 118.97977794460765 31.57053922090137, 118.97818804758278 31.5709856317321 118.97841257328105 31.57106813177895, 118.9953783990435 31.5671095884079 118.99541085997353 31.567109431962315 118.99543851114655 31.56715317208972 118.99541133201843 31.567156005745147 118.99523613153411 31.567399722923504 118.9950044401293 31.567301097379104 118.99483907810446 31.567160741841416 118.99450039346411 31.56693178276318 118.99426831833223 31.5665162554084 118.99396393235308 31.566226264428984 118.99365688430012 31.565943637331845 118.9934282025749 31.565745268005564 118.99313547775043 31.565462503193665 118.99294464429663 31.56513093305223 118.99270381980209 31.564685661015567 118.99272486234354 31.564251682023652 118.99248595362221 31.56411387734667 118.99246862624958 31.564097001653646 118.99246843913679 31.564124663002772, 118.94714725627875 31.574430147172695 118.94703851980132 31.574812703744556 118.94682131619096 31.575384660274565 118.94684486690774 31.575554414747177 118.94673322581225 31.575673524055933, 118.94304840980175 31.572462578807986 118.94291009871361 31.572445848225303 118.94276535590274 31.572412161142996 118.94282482264799 31.572301241677128 118.94289916360609 31.571879916054577 118.94297411558027 31.571434380228002 118.94309676373565 31.571304111560334 118.9433268488018 31.571190416083287 118.94332694530064 31.571218078216813, 118.96824291753441 31.54850252241041 118.9683925311617 31.548328947056312 118.96841968553132 31.548304078207558 118.96861498454274 31.548130098738138 118.96877178436885 31.54795832026126 118.96892643827252 31.547812403880087 118.96906739534921 31.547661021807752 118.96984590648769 31.546883865418483 118.96998418862043 31.54688352468818, 118.98002383580074 31.57026663259032 118.9800238570953 31.570297262192586, 118.99243299619975 31.578621721056795 118.99244651984499 31.57862175947459, 118.99203783415768 31.598894259477408 118.99203781591468 31.598882770059106 118.99203798825737 31.598882785586625 118.99202023564922 31.599154561499404 118.9920201295995 31.59915456169833 118.99202023564922 31.599154561499404 118.99161871783875 31.599038410974718 118.991618611816 31.59903841111932 118.99225221084978 31.59833594353666, 118.99461056105392 31.568098845383425 118.99461059786599 31.568121631905782, 118.9641790168829 31.575618034364236 118.96417905349463 31.57564121955212, 118.96157590999951 31.569610710541163 118.96157608050784 31.56958309396468, 118.97776784322078 31.564147210020074 118.97767398394664 31.563940361176364 118.97771163454557 31.563825474718147 118.97771167130463 31.563848262713353, 118.97801846013795 31.564330502573153 118.97800589334763 31.56431452644417, 118.94282482264799 31.572301241677128 118.94285190207029 31.57230129958633 118.94286981277294 31.572301323121515, 118.96462093519706 31.551489500584836 118.963634834222 31.55073662858252, 118.94711894035336 31.578715959467477 118.94708640327043 31.57871567845837 118.94707637463132 31.578692645172374 118.94727384264564 31.57810737074425 118.9468835367415 31.577652996573146 118.94672478532448 31.57718938101455 118.94673751456884 31.576728922324914 118.94673322581225 31.575673524055933 118.9468466639228 31.575863605614785 118.9467547044346 31.57609750760259 118.94675452775572 31.576125167230884, 118.97647841473706 31.563025082422342 118.97646319486778 31.563010862462274 118.97606978148981 31.56257994672713, 118.97998256198545 31.57078170535429 118.9804301495108 31.570071829303092 118.9804301495108 31.570071829303092 118.98043024086856 31.57007182024192 118.98043180223036 31.570006828563876 118.98035847620683 31.56999581179325 118.98033606759014 31.569995753066753 118.98033607413527 31.570038546977614, 118.98491214179997 31.59500592449034 118.9848986199891 31.59500588811691, 118.98542234650719 31.59503377146025 118.9854223444177 31.595019070248725 118.98542301387279 31.595019072056807 118.98532282791953 31.595073837157567 118.98529675521043 31.595026435580674 118.98528140100258 31.594874119556597 118.98528140100258 31.594874119556597, 118.93634808946351 31.56817408097084 118.93631208303488 31.568174032691395, 118.99379805497193 31.569068004563817 118.99383413528432 31.569068090868853, 118.98491010379928 31.595166943611353 118.98455355274855 31.595327716973856, 118.94975296101272 31.559589344284078 118.94906389963752 31.55981667473509 118.94904820481008 31.55979349814432 118.94968506420375 31.559524467208746 118.94997989240473 31.559317536862306 118.9505032283039 31.558947403288307 118.95077851117202 31.558749416669773 118.95099059919484 31.558873680758282 118.95098529141272 31.55887465622298 118.95103136946845 31.558946972350146 118.95106374323855 31.559008611512468 118.95108896912109 31.559065625428765 118.95108900571398 31.559088415265055, 118.97661332074675 31.585501746191323 118.97662336868797 31.585501793644514 118.97663343325071 31.585501845093802 118.9766334697843 31.5855246278071, 118.97998252174452 31.57073575557458 118.97998255906504 31.570762568964497, 118.97826788715477 31.57026189618877 118.9782678889047 31.570273378108162, 118.97700175370936 31.587962052624746 118.97698823460243 31.58796201861388, 118.97595788560335 31.585293053751307 118.9759576413137 31.585265447438218 118.97608581269144 31.585241956634242 118.97628834233869 31.585004300749723 118.976654205428 31.585176555464745 118.97661332074675 31.585501746191323 118.97671970624958 31.585791842709178 118.9768964748645 31.586433016203944 118.97695183269498 31.58716997174502, 118.97805047403392 31.580022900990944 118.9785391664886 31.580307002372784 118.97941689794081 31.580434381981647, 118.97762889437011 31.57874111371926 118.97761988148841 31.578741090794868 118.97760636217038 31.5787410564133, 118.96479313898607 31.551674511229614 118.96477962548282 31.551674498420248, 118.94779443121101 31.573139792361044 118.94776976193178 31.573176956510153 118.94776984982207 31.57317695506824 118.94751442103774 31.573466474734566 118.9475154268679 31.573466189087753 118.94743285435489 31.57350157368003 118.94725525088076 31.574119187806026 118.94714725627875 31.574430147172695 118.94684854291539 31.574585691871235 118.94667638481742 31.57473916924455 118.9465525147745 31.57497340564137 118.94644655743122 31.57525102071538 118.9464146846282 31.575428256964475, 118.98476311596195 31.592869532340877 118.98476311182613 31.592840629462255 118.98476936550813 31.592840641787745 118.98477060991557 31.592942104987337 118.9853215090939 31.59308432466708 118.9860566826392 31.593462134167417 118.98605659313199 31.593462138531955 118.9860560404911 31.59346099075941 118.98532328656877 31.593085586712625 118.98459896002959 31.59289749141516 118.98445508865998 31.592322810401303 118.9843220034213 31.591665943685474 118.98424053448954 31.59110860504778 118.98420822601081 31.59072934662723 118.98423226189779 31.590189377020387 118.98421202544158 31.589755164851933 118.98419591138837 31.589682064833454 118.98418884745558 31.589668019844453, 118.97818061347583 31.584917114669018 118.97818064338972 31.584894376736177, 118.98026257471801 31.572351706428805 118.98026261202236 31.572378519343268, 118.94596354338915 31.57304081089021 118.94560141944017 31.573046645587443 118.94512437565987 31.57301939863749 118.9448220074226 31.57292376413521 118.9445386030432 31.572852656045153, 118.98485443661775 31.59512059864839 118.98487251081019 31.595120647249782, 118.99171327045508 31.592567954464656 118.99170440108284 31.59256930167564 118.99149161547237 31.592607444195654 118.9906766225312 31.592702817830215 118.99063313659836 31.59270628487575 118.99063512243298 31.592706600220673 118.99063522903427 31.59270659868037 118.98790052995622 31.593079073110882 118.98790042365947 31.593079074002304 118.98789879461589 31.593078249061275 118.9877953229595 31.593136745585173 118.9877953229604 31.593136745585173 118.98632539347675 31.592813183056492 118.9855178187001 31.592635294328844 118.9853192428133 31.592589994214045 118.98523199883535 31.59255202947581 118.98515660865667 31.592497909493346 118.98505090044404 31.59244948610474 118.98493512024498 31.59242326106831 118.98471321706857 31.592388931220302 118.98471577308278 31.592840497666433 118.98471977953572 31.59284051292485 118.98471978367066 31.592869415805048, 118.98051358955293 31.58397551638281 118.98129593386918 31.583798669818304 118.98257857623308 31.583445605980426 118.98332644828156 31.58320853903067 118.98335319110151 31.583203253906927 118.9833085986257 31.583276765954 118.98373465326776 31.58317079780564 118.98376065934434 31.583193991084627 118.98376065821424 31.583186337589364, 118.98794849178088 31.582372235622955 118.98796468622706 31.58236572908329 118.98832419095189 31.582195385808607 118.98830811184669 31.581402903705445 118.98818139113769 31.580532295181467 118.98816409919549 31.57979303777246 118.9881997733355 31.57898376941752 118.98817141847259 31.578324470209015 118.988122425841 31.57719155327243 118.98809925903451 31.577168418208966 118.98810932916054 31.577168474742944 118.9882094180122 31.57716842811815 118.98824200220994 31.57716866017526, 118.98459898943773 31.592870729453264 118.98459898834268 31.592863076762782 118.98459898670058 31.59285159772469, 118.96464382067188 31.57359997142122 118.96467625697638 31.573599912279086 118.96465284033223 31.57337688216792 118.96515682396178 31.57325726182855 118.96558831069572 31.573195842435357 118.96567255305378 31.57319272189001 118.9655956362215 31.572141098078497 118.96558234787919 31.572020835801318, 118.97265950023316 31.559247545143403 118.97265949724445 31.559228331114287 118.97266804349537 31.559228351899417 118.97266804349537 31.559228351899417 118.97266804349537 31.559228351899417 118.9724575106117 31.559316217374565 118.97243162346852 31.559316908732807 118.97233467845264 31.55898404159392 118.97233468068676 31.55898366405872 118.972280619463 31.559029497425595 118.972280619463 31.559029497425595, 118.99072021761295 31.58234722233941 118.99010458894881 31.5824648064351 118.9900763914878 31.582465659966974 118.99010897633323 31.58246596590242, 118.984890143151 31.59373525469679 118.98509152059302 31.593736358103673 118.98506446498867 31.59373629874633, 118.99416206211855 31.57882287526971 118.9941533840713 31.578820773632074 118.99349578652583 31.578633962498145 118.9924115129071 31.578494660036053 118.99239239203182 31.57862158336875 118.99242398043718 31.578621695446206 118.99243299619975 31.578621721056795, 118.9849217272768 31.593735362071676 118.98493074171527 31.593735468707433 118.98494426351805 31.593735471406934, 118.97610655428919 31.5535695158141 118.97610637200482 31.55359718091743, 118.94286981277294 31.572301323121515 118.94288785865767 31.572301346848022, 118.97998252174452 31.57073575557458 118.98005359249183 31.570660285486547, 118.96556463398753 31.552351388520584 118.96555854600781 31.552345730666698 118.96525747754293 31.552017021687472 118.96489252437961 31.55172348703207 118.96509891275544 31.551377170516936 118.96537641913984 31.551126903720693 118.9656285338594 31.5509696627613 118.96589086259713 31.55072028118721 118.96619243766692 31.55042951586161 118.96626512667083 31.550436675460368 118.966313048295 31.55036883356695 118.96682726429259 31.549820358990985 118.96711905958266 31.549546017285255 118.96746074551234 31.54923530379078 118.96775941784514 31.54895559778122 118.96801362316808 31.54871149663294 118.96824291753441 31.54850252241041 118.96845011732792 31.548611570693822 118.96892240021596 31.54883188484579 118.96936820854702 31.549054129656376 118.96983477656255 31.54930098268082 118.97014873925187 31.549475445705614 118.97050512519851 31.549655623120742 118.97099492169973 31.549923080955928 118.97145016573691 31.550151120677963 118.9719753390897 31.550442907274558 118.9723751410434 31.550697459319604 118.97269578273205 31.55097726108814 118.97311046181184 31.551127350150065 118.97351876357895 31.551153579290304 118.97399918282564 31.55135520897436 118.97452907263099 31.5513386550119 118.974529109479 31.551361446159316, 118.9656624640191 31.571719364447343 118.96567306782437 31.57173190028368, 118.94596354338915 31.57304081089021 118.94597254373484 31.573040689096185 118.94599500984104 31.573040291960115 118.94644781427671 31.573050492587562 118.94681572327372 31.573069757306143 118.94727807021978 31.57309498964538 118.9475277439679 31.57310904457888 118.94779443121101 31.573139792361044 118.94796147815642 31.57290363557342 118.9484917641817 31.572947194880978 118.94910136751285 31.572826768793398 118.94962089831195 31.572780790330864 118.94994176722223 31.572819949329975 118.95038246159262 31.572767419480325 118.95087419659625 31.572765045380294 118.95143034330037 31.572704394268964 118.9521473991191 31.572652020459895 118.95259706021123 31.572531835540012 118.95331061823818 31.57287486158571 118.95368174758589 31.572857369938045 118.9540354218241 31.57286866255497 118.95435109931917 31.572881218238226 118.95473997636753 31.572947453708768 118.95508581913265 31.57296035993683 118.95542083878408 31.572977792079776 118.955815758838 31.572947276986053 118.95615279595378 31.572906719969122 118.95681364744425 31.572962280768404 118.95726067176275 31.572988807007945 118.95805346885591 31.573023715626878 118.95845709046955 31.573009880645653 118.95893555296456 31.57302280188413 118.95923790290854 31.57303780815853 118.95961987212348 31.57296434032496 118.95992617974906 31.572639289377168 118.96018246059981 31.572363270068635 118.96047148819662 31.572102400051403 118.96068818930648 31.571869626058458 118.96052078981185 31.57145342957629 118.96058454625242 31.571100412897618 118.96056663321232 31.570631295873387 118.96072193722856 31.57018582861682 118.96102453820583 31.569772192224644 118.96120956510953 31.569479543414126 118.96129273514953 31.569276870539934 118.96143548296745 31.56872712025176 118.9615303174679 31.56843731942972 118.961636669027 31.56815952035337 118.96176321171329 31.56782902771165 118.96190958492956 31.567451352946478 118.96205997172241 31.567053905123572 118.96208773218359 31.566779470332495 118.96210518152678 31.56675440905387, 118.96347501269054 31.57548681362404 118.9639029501188 31.57556089231204 118.96407300678962 31.575104766487318 118.96420137141385 31.574717995093216 118.96373678003779 31.574644477824105 118.96329243885141 31.574495260631554 118.96339350956347 31.57440407485151, 118.98420822601081 31.59072934662723 118.98492666305665 31.590599823079916 118.98507038987923 31.590804242438054 118.98507042697965 31.59083104988295 118.98507042808276 31.590838702736534, 118.96489252437961 31.55172348703207 118.96483364636325 31.55167457782109 118.96466671249055 31.551523617642435 118.9642426991397 31.551034316561946 118.96446571287457 31.5506447346691 118.96472321842816 31.550045078750454 118.9649486635818 31.549608825827942 118.96515364157837 31.54926337449157 118.96500164417088 31.548920004375045 118.9652744988398 31.548466653977982 118.96568474626712 31.548093864410248 118.96587450220609 31.54789046996242 118.96587476448451 31.547918087579227, 118.95368174758589 31.572857369938045 118.95336406191868 31.572204962307133 118.95318454757346 31.571840240523514 118.952943313139 31.571364496979754 118.95254790170843 31.571370907036837 118.95219312199532 31.571291255853676 118.95216701301496 31.57127674327375 118.95216706157068 31.571299530130037, 118.98515773059944 31.59475323772281 118.98518450573361 31.59475326315605, 118.99175622135401 31.57825928101545 118.99179653280324 31.578202344759312 118.99204328594959 31.578298736908675 118.99204332264307 31.57832152098109, 118.96984590648769 31.546883865418483 118.96981340549011 31.546883612333556, 118.99173525014508 31.59256387616259 118.99171327045508 31.592567954464656, 118.99300407918405 31.59024803635235 118.99295783016812 31.59059084061102 118.99293375983794 31.591014783432357 118.992888545851 31.591482616389793 118.99283225652776 31.591953396220788 118.99263215298575 31.592373931616514 118.99240539292491 31.592994662750858 118.99262202913418 31.59386015728305 118.99256981481432 31.59452499548496 118.99256336637124 31.59480127884714 118.99252866240094 31.595130876752542 118.99252516509425 31.59514978558278 118.99252345751368 31.595157294507153, 118.97579215644423 31.57982870878178 118.97653547153507 31.5797860472784 118.97694346839693 31.579772305490863, 118.97698823460243 31.58796201861388 118.97697922186897 31.587961995941995 118.97694764408348 31.587961894164145 118.9769627980353 31.587438999960582 118.97695183269498 31.58716997174502 118.97737781670793 31.58703178168244 118.9777831730241 31.58697309509913 118.978188880339 31.586891605656987 118.97857941126756 31.58682520518759 118.9789620758486 31.58674429988849 118.9793850655629 31.586672838703226 118.97941966334604 31.58666892932299 118.98042324044755 31.586671175163183 118.98042333716737 31.586698831560312, 118.9674057837221 31.554442276226673 118.96731897521147 31.55444236882995 118.96482105929076 31.55155904250069 118.96477962548282 31.551674498420248, 118.98962623319522 31.565545157412714 118.98962622949146 31.56552153415965, 118.97766040624082 31.578741171528794 118.97762889437011 31.57874111371926, 118.961636669027 31.56815952035337 118.96189609602038 31.568346127528862 118.96192956144566 31.56837425027551 118.96196212036303 31.568374289740724, 118.97971697336035 31.584160882317555 118.97969526981876 31.584166058996317 118.97869742887355 31.58438813902929 118.97798608482006 31.584611738558213 118.97774774431639 31.584639943248952 118.97741793890009 31.584744552247063 118.97715420016758 31.584807613985923 118.97685100667951 31.584881467684518 118.97656184140328 31.58493135231854 118.97628834233869 31.585004300749723, 118.98523834764089 31.59093425752717 118.98523834543731 31.59091896786051 118.98523834543731 31.59091896786051 118.98532192678712 31.590958603422457 118.98525871737834 31.59105716778164 118.98512451054901 31.59080441088038 118.98507042973748 31.59085018201576, 118.966313048295 31.55036883356695 118.966313234761 31.550396450634764, 118.97145016573783 31.550151120677963 118.97164173844492 31.550700734033285 118.97167009456277 31.550869808970187 118.97154142783154 31.551334588617166 118.9714294726392 31.551731055835116 118.97157165885376 31.552268171638158 118.97140619807263 31.552669511174745 118.97131765147013 31.553102712261403 118.97124414824673 31.553514285516663 118.9713227373459 31.55354948075357 118.9713327660946 31.55353450192041 118.97133280296417 31.55355769242519, 118.98962622949146 31.56552153415965 118.98959826174405 31.56541183251303 118.98959826174405 31.56541183251303 118.98959835447444 31.565411820507684 118.9895956537147 31.5653726092087 118.98963301699467 31.565354863373702 118.98964041864728 31.565354884193436 118.9896404248259 31.565394273729428, 118.95363051075341 31.57447533097827 118.9536373897204 31.574461278478633 118.95364293544642 31.57444665298871 118.95349213130349 31.574037997986768 118.95325349883566 31.573507825374367 118.95325070848604 31.573426632635588 118.95329334666526 31.57299353186052 118.95331061823818 31.57287486158571, 118.96466671249055 31.551523617642435 118.96465358675317 31.55151304306911, 118.97905063127526 31.556126509008003 118.97905062686182 31.556098635625876 118.97905539387877 31.556098648048078 118.9790545195521 31.55615651278494 118.97903668599227 31.55615564117118 118.97912206156967 31.556089733978414 118.97912206156967 31.556089733978414, 118.99090217341744 31.587086477961797 118.99089321278356 31.58708729534649 118.9905890699568 31.587120062906212 118.99060045515978 31.587377735151378 118.98927425503645 31.587408157299215 118.98930121515978 31.58729755666472 118.98908665976398 31.587299011342648 118.98893892935772 31.58730568256599 118.98829147781716 31.586928585061187 118.98832306408093 31.586928694743428 118.98833207923357 31.58692871966992, 118.99385813021082 31.56902704421891 118.99379805497193 31.569068004563817 118.99365509482914 31.56932578271985 118.99355006477268 31.56978288557433 118.99317687343834 31.56994175668909 118.99279681235106 31.570100428252054 118.9924159666375 31.570259875293026 118.99203591399709 31.570417334122425 118.99160875253357 31.570591747996684 118.9911984973017 31.570755044382814 118.9907089155607 31.570965120887358 118.99053525553347 31.570712826674367 118.99037525248932 31.570407698531866 118.99021767776563 31.570106562588403 118.99004538500928 31.56977550752126 118.9898435395953 31.56940670773119 118.98973769165693 31.56913015455958 118.9895120816903 31.568788641276246 118.98940905470626 31.568564053373873 118.98984526547815 31.568460678761934 118.99014072163655 31.56840973821981, 118.97242521355508 31.558878450247196 118.97228057909449 31.558983541418108 118.97228061648597 31.559010358435867 118.97228061767714 31.559018014032233, 118.94276535590274 31.572412161142996 118.9427926252046 31.572412219227406, 118.98480937587173 31.595120455145807 118.9848364978068 31.59512054011369 118.98485443661056 31.59512058151876, 118.97914629179907 31.555996575777584 118.97912140417534 31.556051793331587 118.97908599214666 31.556089628773144 118.97912206156967 31.556089733978414, 118.98963518210968 31.565545182581435 118.98963517918925 31.565526560184573 118.98963585735531 31.565526549847057 118.98963499943815 31.566461174048168 118.98963490680568 31.56646118580617 118.9896371432487 31.56643641484125 118.9895940180263 31.56627482125962 118.98959405483117 31.56629760835628, 118.97740642121175 31.587701203630598 118.97740641900549 31.587685913630796 118.97740641900549 31.587685913630796 118.97732984536918 31.58767403293259 118.97734164246141 31.58774762310106 118.97708451625289 31.58761692526476 118.97703044700896 31.587662615106414, 118.99092448029005 31.587083978885612 118.99090217341744 31.587086477961797, 118.96498126987002 31.571712895619182 118.96498147708978 31.571685235696624 118.96514170930229 31.57176486972611 118.96533198505139 31.571878632513975 118.96558234787919 31.572020835801318 118.9656624640191 31.571719364447343 118.96575530773252 31.571372083564903, 118.98528140100258 31.594874119556597 118.98526787908392 31.59487408307981, 118.99667066754829 31.568523104410698 118.99666190076007 31.56852128982237 118.99664021761292 31.568515863083768 118.9960114959613 31.56848901383045 118.99466797109196 31.56799584966257 118.99461056105481 31.568098845383425 118.9945580405289 31.568149191728168 118.99442883712976 31.568436505659385 118.99428561047117 31.568821572690844 118.99412958566384 31.569150314583823 118.99400126720946 31.569552366266144 118.99387567591269 31.569819010199677 118.99358141136864 31.569786310290578 118.99357244790147 31.569785459273913, 118.97399918282564 31.55135520897436 118.97480585200435 31.55166932109181 118.97510598685915 31.551897374737294 118.97520928005711 31.55213089656134 118.97541266566229 31.552417058072106 118.97567091397893 31.552867236127597 118.97593976824595 31.55329393375764 118.97610655428919 31.5535695158141 118.9761227226177 31.553591925851197, 118.9907089155607 31.570965120887358 118.99052841860856 31.570988811640543, 118.99315983542023 31.58873464403477 118.9931157569721 31.58871624807439 118.9931061911567 31.588732633325044 118.99275362629653 31.588537811939133 118.99275785603112 31.588528959360048 118.99270684348821 31.588463645214446 118.9920564980905 31.58816083666274 118.99163903725616 31.587707910038308 118.99159673850536 31.587707668403933 118.99092448029005 31.587083978885612 118.99057872769963 31.586502715746178 118.99039306938629 31.58622677695265 118.9902039289864 31.585836455371787 118.99019788349162 31.585814414973694 118.99019792009258 31.58583719724861, 118.94286850945221 31.56746792723027 118.94340508745918 31.567094759122533 118.94340493710817 31.56712242171106, 118.94297411558027 31.571434380228002 118.9429897978906 31.571420653722598, 118.98492172719597 31.593735426137133 118.984890143151 31.59373525469679, 118.94644655743122 31.57525102071538 118.94644637884163 31.575278680591808, 118.94719273456263 31.56125435670892 118.94723181901287 31.5612666901544 118.94723185561026 31.561289879355563, 118.97998255906504 31.570762568964497 118.97998256023284 31.570770223521176 118.97998256198545 31.57078170535429, 118.97380768738086 31.582475597849868 118.9735148087639 31.582735827983438 118.97357354415922 31.58287825666018 118.97372977116206 31.582908972720965 118.97376234339426 31.582909224059794, 118.96483364636325 31.55167457782109 118.9648021479996 31.551674531004444 118.96479313898607 31.551674511229614, 118.99418336927137 31.57882908955475 118.99416206211855 31.57882287526971, 118.96415857710201 31.575671873316196 118.9641790168829 31.575618034364236 118.96548979356972 31.576189872125692 118.96589771809651 31.57634574989062 118.9664285858663 31.57654597666002 118.9666181008771 31.576979942510903 118.96725461791743 31.577243687290906 118.96788596308627 31.577237838758666 118.96889233957 31.577436843153833 118.96944390067104 31.577595191024344 118.97013380048519 31.577963323501244 118.97098552333928 31.578305534528482 118.97160895577879 31.578570385184015 118.97167334090082 31.578597704739057 118.97170856602602 31.578627886285314 118.97295289024753 31.5790676369866 118.97396641646394 31.579451315436128 118.97446090179005 31.57957341092307 118.97524009068765 31.579848913550702 118.97579215644423 31.57982870878178 118.97578542537322 31.580718409644334 118.97548068027454 31.58110575531722 118.9752747578854 31.581314566947025 118.9749580637849 31.581708878094005 118.97487952673501 31.58171319061283 118.97480550550259 31.581666199195716 118.97442631811856 31.582041784193592 118.97401410158504 31.582249857512892 118.97384130249061 31.582347826944105 118.97380768738086 31.582475597849868 118.97439657174908 31.58225789352169 118.97438951932014 31.582276872619335 118.97438975980634 31.582304479871322, 118.99276368693724 31.583529240445714 118.99309016100521 31.583607102058664 118.99318344328965 31.583663637601727 118.99326636813538 31.583821376888036 118.99326838022685 31.5838311658713 118.99330605648527 31.58387106951622 118.99348052959382 31.583964380653256, 118.98459896002959 31.59289749141516 118.98459898943773 31.592870729453264, 118.97831897344936 31.57099891030645 118.97818804758278 31.5709856317321, 118.98961728103245 31.565545132236334 118.98961727811383 31.565526509838705 118.98961300245288 31.56552648557466 118.98963613501559 31.565526921099362 118.98963619865941 31.56646261769512 118.98970754263111 31.56657519189916 118.98970745000224 31.56657520364414 118.98965015011473 31.56656498542754 118.98969201516789 31.56671017886591 118.98974071608325 31.56688953212177 118.98979341765924 31.567092605635548 118.98983893216047 31.56728137152859 118.98992500486555 31.56759232378631 118.99003509529818 31.5680008194807 118.99014072163655 31.56840973821981 118.99079843966857 31.56828037508662 118.99084622789293 31.5682782958107, 118.96489252437961 31.55172348703207 118.96489799767627 31.551532873767325 118.9648982637932 31.55150525261117, 118.96567306782437 31.57173190028368 118.96568517538334 31.57174316256677 118.96622044533366 31.572328365862603, 118.9768177132906 31.581616953407114 118.97681784858138 31.58158929631604 118.97684803533014 31.58158244204167 118.97699737513112 31.58100057148225 118.97697519381514 31.580394914103604 118.97694346839693 31.579772305490863 118.9775825555029 31.57980279339303 118.97805047403392 31.580022900990944 118.97810157128798 31.579852644149618 118.9781411416235 31.579731572833904 118.97792977537456 31.579264186466162 118.97766040624082 31.578741171528794 118.97748288797997 31.57837388696047 118.97737671576505 31.57811611632889, 118.9895120816912 31.568788641276246 118.98951211853111 31.568811827501985, 118.98507042808276 31.590838702736534 118.98507042973748 31.59085018201576, 118.98564729307786 31.58234609538744 118.98564729009186 31.582325975692477 118.98565004128932 31.582325983177544 118.98565004128932 31.582325983177544 118.98565013192933 31.582325976101114 118.98564884301938 31.58232596527265 118.98573186050388 31.58233759848744 118.9866683821244 31.582441763841736 118.98666831028973 31.582457089379936 118.9867223763015 31.582412241304553 118.9867223763015 31.582412241304553, 118.97774774431639 31.584639943248952 118.97818061347583 31.584917114669018 118.9782943294667 31.58497008707456, 118.98466674028573 31.591777530042336 118.98459898670058 31.59285159772469, 118.9769627980353 31.587438999960582 118.9770304071561 31.587616673510883 118.97703044424935 31.587643482129256 118.97703044535336 31.587651135321195, 118.99840375941612 31.568845449351777 118.99885756251878 31.56897888002396, 118.99356356540575 31.58477595535356 118.99351645248274 31.58511094408646 118.99345899254688 31.58551950352235 118.99338667824016 31.58631824865293 118.99334551352436 31.586897006944234 118.99329930236614 31.58739795414295 118.99323164695157 31.587897077841173, 118.98026261202236 31.572378519343268 118.98026261318388 31.572386173763462 118.98026266583236 31.572351697885225 118.97864937810037 31.571832025074844 118.97850677240851 31.571748745774208 118.97835472777031 31.57100234788272 118.97965566108925 31.5716034883131 118.9796552784878 31.571444420091 118.97965467389645 31.571296609601095 118.97962940514 31.571123880842567 118.97969990100327 31.570998462156755 118.97974825298603 31.570848834232486 118.97978298126112 31.570667447818675 118.97980471620515 31.57053926834153 118.97924528458212 31.57045897703706 118.97826784867365 31.57022742798689 118.97826788598786 31.570254241575782 118.97826788715477 31.57026189618877, 118.94182248098689 31.568253458293434 118.94221224142413 31.568004670861846, 118.94309669270494 31.569345520603495 118.94309669000013 31.569326623554364 118.94308078839128 31.569332898466826 118.94306068848438 31.569205903554703 118.9428943184941 31.56906691411118 118.94289423066131 31.569066915542454 118.94286153460625 31.56901663407108 118.94254891303876 31.568872606349277 118.9424202515573 31.568784118359325 118.94214518827154 31.568604141889733 118.94182248098689 31.568253458293434 118.9414690206501 31.56832903790667 118.94059577119378 31.568309370417932 118.94013717827222 31.568268192955085 118.93964430271365 31.568159757620638 118.9391732918643 31.567991159992424 118.93867560386272 31.567811904914794 118.93817175143842 31.567643267899278 118.9376981839748 31.56743632413395 118.93715013041945 31.56765775047604 118.93679865644101 31.56777438075051 118.93631103343073 31.567971970634897 118.93631208303577 31.568174032691395 118.93611468998047 31.56842624324023 118.93617581755707 31.568634569673232 118.93618260950244 31.568727159054806 118.93631165663875 31.568785027342415 118.93648145550206 31.56889497700907 118.93612148187911 31.569045434370576 118.93605356230503 31.569317414236348 118.93573700472061 31.569502631316276 118.93531323917183 31.569890305539673 118.9348649701342 31.570046548066973 118.93431482179386 31.570000254167866 118.93394126425682 31.570214363925253 118.93355000469754 31.570312997075938 118.9335500410299 31.570335784666806, 118.94305117746441 31.56934546019965 118.94305117475868 31.569326563148962 118.94304827307366 31.56933470788151 118.94309802202203 31.56933533843406 118.94333749446136 31.571123431027278 118.94332817257089 31.571131596912675 118.94333026418842 31.571131312251378 118.94299664268298 31.571415692827063 118.9429897978906 31.571420653722598, 118.97612043203323 31.562378050325798 118.97612039527145 31.562355261930072 118.97615085963469 31.562529756932037 118.97606978148981 31.56257994672713 118.97564843774423 31.562133676368447 118.97526891054548 31.561696081466586 118.97488884668512 31.56127604516548 118.9744717226714 31.56085466233381 118.9741172451345 31.560521405549725 118.97372771727554 31.560124356062413 118.97341613846527 31.559826163117286 118.97300751738298 31.55945703974547 118.97273017559688 31.559217599457387 118.97242521355508 31.558878450247196 118.9720285021931 31.55845499786098 118.97161107051087 31.558069074237142 118.97119728171184 31.557675567843525 118.97085110554511 31.557295794613562 118.97042363449786 31.556887842250372 118.9700414566555 31.556482121363636 118.96959389642096 31.556062094153933 118.96918658276022 31.555639265333912 118.96879940608973 31.55522355366469 118.9684374188832 31.554916977906085 118.96824190872816 31.554695075628132 118.96806167362683 31.554495880597564 118.96799477875112 31.55443542515047 118.96792753533359 31.554376340826074 118.96786046666888 31.554316571340685 118.96779339832669 31.554256802123174 118.96769269703779 31.55416712647281 118.96738067519817 31.553910574294882 118.96704927589565 31.55367402903551 118.96674225225621 31.553443643829734 118.96629270115017 31.55315206051956 118.96602942052884 31.55283247231321 118.96573423127698 31.552535774529638 118.96557819507002 31.55236667663305 118.96556463398753 31.552351388520584, 118.98472845012785 31.592869439109304 118.9847284459929 31.59284053622913 118.98472649155978 31.5928405264861 118.98457023126987 31.59283800549116 118.98470723458075 31.593552757639696 118.98517289047746 31.593573084616725 118.98517280107083 31.593573088770835 118.98517171048103 31.593573904259152 118.98504314267052 31.593596070017 118.984890143151 31.59373525469679 118.98491217023344 31.59421951371399 118.98505350918846 31.594527826555332 118.98518450573361 31.59475326315605 118.98522728011181 31.594873951218794 118.98571900556095 31.595148245272235 118.98495267401825 31.595006011179184 118.98480937587173 31.595120455145807 118.98491006404252 31.59512100615238 118.98491010108367 31.595147812362 118.98491010216972 31.59515546486175, 118.99541133201843 31.567156005745147 118.99541136947639 31.56718281971746 118.99541137067834 31.567190474437925, 118.97847280336883 31.564889608856014 118.97884763178934 31.564620638741378 118.97924312068524 31.56428269291794 118.97931401696445 31.56422787770978, 118.96208773218359 31.566779470332495 118.9621145545287 31.566779548586965, 118.99386431564514 31.568947825141585 118.99386430995072 31.5689113450732 118.99387214180025 31.568911367564507 118.99378620885685 31.56897631627799 118.99375606524428 31.569020961172722 118.99383413530497 31.5690681193239 118.99383413530497 31.5690681193239, 118.96622044533459 31.572328365862603 118.96617450111523 31.57244394175322 118.96617450111523 31.57244394175322 118.96617450111523 31.57244394175322 118.96617459058923 31.572443936770053 118.96618318435746 31.57241557094023 118.96621282372588 31.572398086351665 118.96622013754391 31.572398102385655 118.96621858431637 31.57239759445461 118.96621859017158 31.57243698321223, 118.96472321842816 31.550045078750454 118.96457125730994 31.55007407965176 118.96436831791323 31.550042536499916, 118.945026588177 31.561855158234735 118.94502655160387 31.561831969113623 118.94503776940672 31.56186376145788 118.94527412106548 31.562364913792905 118.9457230245257 31.56256072778688 118.94642811522773 31.562354620259935 118.94663513342768 31.562055419821696 118.94693806585366 31.561622331855588 118.94716746187873 31.56129235139228 118.94719273456263 31.56125435670892 118.94744630668106 31.56092373434922 118.9476780160899 31.560563862200215 118.94810369458851 31.560319038556347 118.94852732302475 31.560045062880707 118.94906459277671 31.559816849126634 118.94975296101272 31.559589344284078, 118.97810157128798 31.579852644149618 118.978128688656 31.579852735777937, 118.99323164695157 31.587897077841173 118.9932304381408 31.587904658198667 118.99322791316607 31.58792364457671 118.99315983542023 31.58873464403477 118.992977380925 31.589166693047602 118.99292915614816 31.589625851584444 118.99300407918405 31.59024803635235 118.99246507481848 31.590228763883772 118.99191091491289 31.590086401563045 118.99186156755007 31.590077117332843 118.99186147414594 31.590104771645464, 118.98834560196428 31.58692875706197 118.98833207923357 31.58692871966992, 118.97978167958613 31.57006403605695 118.9797816765094 31.570043904022963 118.97978337575181 31.570043908453652 118.97977090927276 31.57010616031037 118.9798229888148 31.570104964857215 118.9800238570953 31.57029726219335 118.9800238570953 31.57029726219335, 118.97723484362398 31.58806043195793 118.97723484072601 31.588040312680235 118.97723759135394 31.588040319616923 118.97723759135394 31.588040319616923 118.97723838577366 31.5881128930102 118.97721019804052 31.588112693916052 118.97694768392735 31.58800783559482 118.97700175370936 31.587962052624746 118.97700175370936 31.587962052624746, 118.96597244185283 31.571487221172667 118.9659198782949 31.571461630603867 118.96575530773252 31.571372083564903 118.96567682985379 31.57111155083908 118.96563353172813 31.57084640615951 118.96559835199747 31.570643512329518 118.96558211521194 31.570408339379693 118.96547386983805 31.57032533704138 118.96550643319038 31.570325380395367, 118.96120956510953 31.569479543414126 118.96157590999951 31.569610710541163 118.9616015117219 31.569562958215855, 118.96420137141385 31.574717995093216 118.96444491825824 31.5744062244815 118.96461681543605 31.57412691247778 118.96465795819341 31.573754000166513 118.9646730479094 31.573626575436442 118.96467410742198 31.573618976505426, 118.99466797109196 31.56799584966257 118.99523613153411 31.567399722923504, 118.99246132416081 31.595750817477366 118.99246167068415 31.5957317117396, 118.98388153725624 31.582841711983992 118.98385469645343 31.582841617592965 118.98383851722654 31.582967579610642 118.98376062102041 31.5831595279022 118.98496005231107 31.58274033355766 118.98553432262307 31.582535452955916 118.98562916273352 31.582557365474337 118.98655497354329 31.58244419463551 118.98729370411442 31.582292848239558 118.98793295377055 31.58238010338934 118.98876681197858 31.582558572428862 118.98914697090856 31.582883973228356 118.98956177716106 31.583044586327365 118.99010183230845 31.58316865143574 118.99030977549737 31.582916869229614 118.9905909036267 31.5825553880396 118.99072021761295 31.58234722233941 118.99122154018468 31.582376383152262 118.9915704984078 31.58239385110887 118.99204753798216 31.582469289721878 118.99240925598157 31.58250729379352 118.9922029231459 31.582813635227097 118.99217774126821 31.582818491193457 118.99221026799981 31.58281845641697, 118.99263215298575 31.592373931616514 118.9926108252822 31.592380011143128, 118.98940905470626 31.568564053373873 118.98874686816745 31.568574304617886 118.9881229430101 31.568592226802327 118.98751741629299 31.56860074723235 118.98750840261 31.568600851198905, 118.99252345751368 31.595157294507153 118.99248113383194 31.595376314943543 118.99246298936517 31.59565271241181 118.99246182334565 31.59572406090394 118.99246167068415 31.5957317117396, 118.98010508373258 31.57426204559936 118.9801051204045 31.57428483097066, 118.9924115129071 31.578494660036053 118.99228402481764 31.578411245302412 118.99224032399339 31.578399605051192 118.99175622135401 31.57825928101545 118.99145452356447 31.578164532943934 118.99141924360343 31.578150324636095 118.99145182953492 31.578150575100782, 118.93634808946351 31.56817408097084 118.93650987318237 31.568346144156727, 118.9800961171842 31.57429841263577 118.98010508373258 31.57426204559936 118.9802206404209 31.57377711833383 118.98035755710544 31.57334673575112 118.98031275661303 31.57281608817224 118.98026257471801 31.572351706428805 118.98004690954444 31.571995308810354 118.98001539597053 31.571723611672407 118.98000736281037 31.571455458022417 118.97999782636808 31.57121517840654 118.97998252174452 31.57073575557458 118.98002383580074 31.57026663259032 118.97995006846185 31.570113323013377 118.97976579236288 31.56940042800034 118.97965973016824 31.56923971902473 118.9796455791414 31.56909117664105 118.97963218597377 31.568995331799933 118.97958632055106 31.56865349071174 118.97956290559937 31.568558610959002 118.97945126938173 31.568084120804162 118.97935852739444 31.56753350440026 118.97925776762432 31.56710186787417 118.97915096911747 31.566662195846778 118.97910962700325 31.56636074019411 118.97914167567663 31.56570691275791 118.97875748459383 31.56528723078564 118.97847280336883 31.564889608856014 118.97802413782291 31.56433646304682 118.97801846013795 31.564330502573153, 118.98491010216972 31.59515546486175 118.98491010379928 31.595166943611353, 118.99242444946016 31.578662283115623 118.99243408886916 31.578624905350104 118.99243408886916 31.578624905350104 118.99241110070345 31.578678855854985 118.99234643730668 31.578635184002 118.99239234351022 31.578667527920977 118.99244651984499 31.57862175947459, 118.99494498402102 31.57814749390459 118.99546431313844 31.578136285426567 118.99590903167834 31.578156554550528 118.99665609744031 31.57817537437984 118.99668994227981 31.57767257865834 118.99672994591772 31.577181431949953 118.99674694647788 31.576676869957282 118.99678135282554 31.576182133807883 118.99681297893049 31.575703592853994 118.99682960288993 31.5752050427512 118.99682172569436 31.57507691593315 118.99639197159051 31.57378136574296 118.99647496785143 31.573667689710145 118.99680903261618 31.573131312660113 118.99717080483431 31.572614271709188 118.99760080749842 31.572121530345722 118.99784766667919 31.571492294005733 118.99764614197456 31.570785841093127 118.99794919219931 31.569994004333132 118.99807518728608 31.569663291580746 118.99822760646069 31.569349522176793 118.99840375941434 31.568845449351777 118.99785457491147 31.56873178191806 118.9972472017693 31.568663023312347 118.99667066754829 31.568523104410698, 118.98494426351805 31.593735471406934 118.98472046982543 31.593849513213282, 118.98376065821424 31.583186337589364 118.98376062102041 31.5831595279022, 118.97819487286911 31.570468866173677 118.9782678889047 31.570273378108162, 118.9926108252822 31.592380011143128 118.99260214134199 31.592382042870973 118.99177758162868 31.592609099277787 118.99176158543264 31.592594709125706 118.99173525014508 31.59256387616259 118.99112896304182 31.5923632814942 118.9905156098013 31.592153607489593 118.99036273539647 31.592136375125424 118.99035065644165 31.592111312437968 118.99031813816049 31.592111021786618, 118.94572437721335 31.56851388616479 118.94580411445378 31.568561526154078 118.94577164501762 31.56856155210878, 118.94182248098689 31.568253458293434 118.94176968943296 31.568486733479816 118.94157454831571 31.5686288834777 118.9414684369967 31.56872131770556 118.94135565474974 31.568821369207377 118.94135568467263 31.568798626256637");

        List<List<Double>> bufferPolygon = BufferPolygonCreator.createBufferPolygon(split, 1500);

    }



}


