package com.ruoyi.service.map;



//import com.graphhopper.util.shapes.GHPoint;

import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.*;
import com.ruoyi.graph.vo.NodeVo;

import java.util.List;

public interface IMapService {


    List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo);

    /**
     * 根据杆塔id查询两端导线
     */
    List<WireEndVo> selectWireEnd(String psrId);


    /**id和设备类型判断他的真正psrId
     */
    String selectPsrId(String psrId, String psrType);

    /**
     * 查询运方调整转供路径
     */
    List<NodeVo> transferSupply(String feederId, String closeId, String openId);

    /**
     * 获取联络开关信息列表
     *
     * @param feederId 线路ID
     * @return 联络开关信息列表
     */
    List<ContactSwitchInfoVo> getContactSwitchInfo(String feederId);

    /**
     * 计算基于转供路径的负载率变化
     * 根据转供路径计算当前线路转供至另一条线路的负载率变化
     *
     * @param feederId 源线路ID
     * @param closeId  联络开关ID（需要闭合的开关）
     * @param openId   主干开关ID（需要断开的开关）
     * @return 负载率变化计算结果
     */
    FeederTransferCap calculateLoadRateChange(String feederId, String closeId, String openId);

    /**
     * 查询附近线路展示信息
     * 根据线路ID和半径查询附近线路的详细信息，包括ID、名称、负载率、是否重过载、所属母线、所属隔离开关
     *
     * @param feederId 线路ID
     * @param radius   查询半径（米）
     * @return 附近线路信息列表
     */
    List<NearbyLineInfoVo> getNearbyLinesInfo(String feederId, Double radius);

    List<NearbySubstationInfoVo> queryNearbySubstations(String feederId);

    List<NearbySubstationInfoVo> queryNearbySubstationsByNet(String feederId);

    /**
     * 查询附近设备信息
     * 包括物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
     *
     * @param feederId 线路ID
     * @return 附近设备信息列表
     */
    List<NearbyDeviceInfoVo> queryNearbyDevices(String feederId);

}

