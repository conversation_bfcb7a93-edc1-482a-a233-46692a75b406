package com.ruoyi.entity.map.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 附近设备信息视图对象
 * 包括物理杆塔wlgt、运行杆塔0103、环网柜zf07、开关站zf04
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NearbyDeviceInfoVo {

    /**
     * 设备ID
     */
    private String psrId;

    /**
     * 设备类型
     */
    private String psrType;

    /**
     * 设备类型名称
     */
    private String psrTypeName;

    /**
     * 设备名称
     */
    private String psrName;

    /**
     * 区域ID
     */
    private String zoneId;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 端口列表
     */
    private String portList;

    /**
     * 端口名称列表
     */
    private String portNameList;

    /**
     * 电压等级名称
     */
    private String vlevelName;

    /**
     * 电压等级编码
     */
    private String vlevelCode;

    /**
     * 坐标位置
     */
    private String coordinate;

    /**
     * 所属馈线ID
     */
    private String feederId;

    /**
     * 所属馈线名称
     */
    private String feederName;

    /**
     * 带电状态
     */
    private String chargedState;

    /**
     * 开关状态
     */
    private String switchStatus;

    /**
     * 配网标识
     */
    private String distribution;

    /**
     * 维护班组ID
     */
    private String maintCrew;

    /**
     * 维护班组名称
     */
    private String maintCrewName;

    /**
     * 维护机构ID
     */
    private String maintOrg;

    /**
     * 维护机构名称
     */
    private String maintOrgName;

    /**
     * 地市机构ID
     */
    private String cityOrg;

    /**
     * 地市机构名称
     */
    private String cityOrgName;

    /**
     * 省份ID
     */
    private String provinceId;

    /**
     * 跨馈线ID
     */
    private String crossFeederId;

    /**
     * 跨馈线省份
     */
    private String crossFeederProvince;

    /**
     * 是否省级容器
     */
    private String isProvinceContainer;

    /**
     * 方向
     */
    private String direction;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 距离（米）
     */
    private Double distance;
}
